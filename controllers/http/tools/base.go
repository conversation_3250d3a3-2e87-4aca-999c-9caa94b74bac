package tools

import (
	"fwyytool/controllers/http/tools/gray"
	"github.com/gin-gonic/gin"
)

func RegisterHandlers(rg *gin.RouterGroup) {
	toolsRouterGroup := rg.Group("tools")
	arkRouterGroup := toolsRouterGroup.Group("mysql")
	{
		arkRouterGroup.GET("slowlist", MysqlController.SlowList)
		arkRouterGroup.GET("slowstat", MysqlController.SlowStat)
		arkRouterGroup.GET("slowcollect", MysqlController.SlowCollect)
		arkRouterGroup.GET("slowstatapi", MysqlController.SlowStatApi)
		arkRouterGroup.GET("slowlistapi", MysqlController.SlowListApi)
	}

	grayRouterGroup := toolsRouterGroup.Group("gray")
	{
		grayRouterGroup.GET("checkclusterinfo", gray.CheckClusterInfo)
	}

	httpTestGroup := toolsRouterGroup.Group("httptest")
	{
		httpTestGroup.POST("call", HttpTestController.Call)
		httpTestGroup.Any("router", HttpTestController.Router)
		httpTestGroup.GET("getreportlist", HttpTestController.GetReportList)
	}

	testGroup := toolsRouterGroup.Group("/cache")
	testGroup.POST("/clearQWCache", HttpTestController.ClearQWUrlCache)

	diffGroup := toolsRouterGroup.Group("/diff")
	diffGroup.POST("/dodiff", DiffController.DoDiff)
}
