package tools

import (
	"fwyytool/components"
	"fwyytool/service/tools/diffJob"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
)

var DiffController diffController

type diffController struct {
}

func (s diffController) DoDiff(ctx *gin.Context) {
	err := diffJob.DiffJobService.Do(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
	return
}
