{{ define "desk/datadiff/diffdetail.html"}}
<!DOCTYPE HTML>
<head>
    <title>流量diff详情</title>
</head>
<link rel="stylesheet" href="/fwyytool/assets/css/bootstrap.min.css?12223"/>
<meta charset="UTF-8">

<script src="/fwyytool/assets/js/jquery-2.1.4.min.js"></script>
<script src="/fwyytool/assets/js/bootstrap.min.js"></script>
<script src="/fwyytool/assets/layer/layer.js"></script>
<style type="text/css">
    table.taskList th {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #dedede;
    }

    table.taskList td {
        border-width: 1px;
        padding: 8px;
        border-style: solid;
        border-color: #666666;
        background-color: #ffffff;
    }

    div {
        margin-left: 10px;
    }

    .nav {
        padding-left: 0;
        margin-bottom: 0;
        list-style: none;
    }

    ol, ul {
        margin-top: 0;
    }

    a {
        color: #555;
        font-size: 16px;
    }

    .active {
        border-bottom: 3px solid #409c19;
        font-weight: bolder;
    }
    textarea {
        resize: both; /* 允许调整大小 */
        overflow: hidden; /* 防止内容溢出 */
        min-width: 100px; /* 最小宽度 */
        min-height: 50px; /* 最小高度 */
        max-width: 100%; /* 最大宽度 */
        width: 100%; /* 宽度100% */
        height: 200px; /* 高度自适应 */
        box-sizing: border-box; /* 包含padding和border在内的总宽度 */
        padding: 10px; /* 内边距 */
    }
</style>

<br>
<td>
    <a href="/arkgo/tool/getdiffcount" style="color:red; font-size: 18px; font-weight: bold;" onclick="toggleData(event)">  昨日小计</a><br>
    <span id="data"></span>
</td>


<form class="form-inline" style="margin-left: 5px;">
    <h3 style="margin-left: 5px">diff 结果查询</h3>
    <div class="form-group">
        <label for="timeRange">时间范围{{.param.TimeRange}}</label>
        <select class="form-control" id="timeRange" name="timeRange" >
            <option value=1 {{if eq .params.TimeRange 1}}selected{{end}}>一天内的数据</option>
            <option value=2 {{if eq .params.TimeRange 2}}selected{{end}}>两天内的数据</option>
            <option value=3 {{if eq .params.TimeRange 3}}selected{{end}}>三天内的数据</option>
            <option value=4 {{if eq .params.TimeRange 3}}selected{{end}}>四天内的数据</option>
            <option value=5 {{if eq .params.TimeRange 3}}selected{{end}}>五天内的数据</option>

        </select>

        <label for="handler">接口名 后缀0:新老diff，后缀1:环比diff</label>
        <select class="form-control" id="handler" name="handler" >
            <option value="StudentCallInfo_0" {{if eq .params.Handler "StudentCallInfo_0"}}selected{{end}}>StudentCallInfo_0</option>
            <option value="UserInfo_0" {{if eq .params.Handler "UserInfo_0"}}selected{{end}}>UserInfo_0</option>
            <option value="AllowAutoCallAndMessage_0" {{if eq .params.Handler "AllowAutoCallAndMessage_0"}}selected{{end}}>AllowAutoCallAndMessage_0</option>
            <option value="GetOptions_0" {{if eq .params.Handler "GetOptions_0"}}selected{{end}}>GetOptions_0</option>
            <option value="GetCustomTag_0" {{if eq .params.Handler "GetCustomTag_0"}}selected{{end}}>GetCustomTag_0</option>
            <option value="GetWxBindInfo_0" {{if eq .params.Handler "GetWxBindInfo_0"}}selected{{end}}>GetWxBindInfo_0</option>
            <option value="InterviewRecordV2_0" {{if eq .params.Handler "InterviewRecordV2_0"}}selected{{end}}>InterviewRecordV2_0</option>
            <option value="InterviewReferLpc_0" {{if eq .params.Handler "InterviewReferLpc_0"}}selected{{end}}>InterviewReferLpc_0</option>
            <option value="GetSchemaByCourseId_0" {{if eq .params.Handler "GetSchemaByCourseId_0"}}selected{{end}}>GetSchemaByCourseId_0</option>
            <option value="GetSipInfo_0" {{if eq .params.Handler "GetSipInfo_0"}}selected{{end}}>GetSipInfo_0</option>
            <option value="GetStudentCallRecordInfo_0" {{if eq .params.Handler "GetStudentCallRecordInfo_0"}}selected{{end}}>GetStudentCallRecordInfo_0</option>
            <option value="CourseRecordDefaultOption_0" {{if eq .params.Handler "CourseRecordDefaultOption_0"}}selected{{end}}>CourseRecordDefaultOption_0</option>
            <option value="CourseRecordMeta_0" {{if eq .params.Handler "CourseRecordMeta_0"}}selected{{end}}>CourseRecordMeta_0</option>
            <option value="CourseRecordV2_0" {{if eq .params.Handler "CourseRecordV2_0"}}selected{{end}}>CourseRecordV2_0</option>
            <option value="DetailConfig_0" {{if eq .params.Handler "DetailConfig_0"}}selected{{end}}>DetailConfig_0</option>
            <option value="GetCourseTimeTableDay_0" {{if eq .params.Handler "GetCourseTimeTableDay_0"}}selected{{end}}>GetCourseTimeTableDay_0</option>
            <option value="GetCourseTimeTableWeek_0" {{if eq .params.Handler "GetCourseTimeTableWeek_0"}}selected{{end}}>GetCourseTimeTableWeek_0</option>
            <option value="GetStudentOrderList_0" {{if eq .params.Handler "GetStudentOrderList_0"}}selected{{end}}>GetStudentOrderList_0</option>
            <option value="KeyBehavior_0" {{if eq .params.Handler "KeyBehavior_0"}}selected{{end}}>KeyBehavior_0</option>
            <option value="PerformanceV1_0" {{if eq .params.Handler "PerformanceV1_0"}}selected{{end}}>PerformanceV1_0</option>
            <option value="StudentDelaminationDiffrenceListV1_0" {{if eq .params.Handler "StudentDelaminationDiffrenceListV1_0"}}selected{{end}}>StudentDelaminationDiffrenceListV1_0</option>
            <option value="StudentDetailV1_0" {{if eq .params.Handler "StudentDetailV1_0"}}selected{{end}}>StudentDetailV1_0</option>
            <option value="GetStudentBind_0" {{if eq .params.Handler "GetStudentBind_0"}}selected{{end}}>GetStudentBind_0</option>
            <option value="GetActiveWithBindData_0" {{if eq .params.Handler "GetActiveWithBindData_0"}}selected{{end}}>GetActiveWithBindData_0</option>
        </select>
    </div>


    <button type="submit" class="btn btn-success"> 查 询</button>
    <br/>
    <label style="color: #ac2925">{{.error}}</label>
</form>


<ul class="nav nav-tabs" id="tabService">
    <li role="presentation" class="active" >
        <a class="chapterTaskList">diff结果 展示前 100 条有 diff 的详细记录</a>
    </li>
</ul>


<div style="overflow-y: scroll; margin-top: 8px; margin-right: 10px; text-align: center;">
    <table class='table table-bordered table-striped table-hover' style="font-size:16px; display: inline-block;">
        <tr>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>总 diff 任务数</th>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #FF0000;color:white'>有 diff 的任务数</th>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>无 diff 的任务数</th>
            <th style='height:24px;font-size:16px;text-align:center;background-color: #22763d;color:white'>未回放完成的任务数</th>
        </tr>
        <tr style="height: 46px">
            <td>{{.data.Total}}</td>
            <td>{{.data.HasDiffNum}}</td>
            <td>{{.data.NoDiffNum}}</td>
            <td>{{.data.UnFinishedTask}}</td>
        </tr>
    </table>
</div>

<br><br>

<script>
    let dataLoaded = false;

    function toggleData(event) {
        event.preventDefault(); // 阻止默认链接行为

        const dataSpan = document.getElementById('data');

        // 如果数据区域当前可见，则隐藏它
        if (dataSpan.style.display !== 'none') {
            dataSpan.style.display = 'none';
            return;
        }

        // 如果数据已经加载过，则仅显示它
        if (dataLoaded) {
            dataSpan.style.display = 'block';
            return;
        }

        // 第一次点击，加载并显示数据
        fetch('/arkgo/tool/getdiffcount')
            .then(response => response.json())
            .then(data => {
                if (data.errNo === 0 && data.data) {
                    // 从 data 字段提取数据
                    const statsData = data.data;

                    // 转换数据为数组以便排序
                    const sortableData = Object.entries(statsData).map(([key, value]) => {
                        return {
                            name: key,
                            ...value
                        };
                    });

                    // 按 hasDiffCnt 从大到小排序
                    sortableData.sort((a, b) => b.hasDiffCnt - a.hasDiffCnt);

                    // 创建表格 HTML
                    let tableHtml = `
                <table style="width: 100%; border-collapse: collapse; margin-top: 10px; font-family: Arial, sans-serif;">
                    <thead>
                        <tr style="background-color: #4CAF50; color: white;">
                            <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">接口名称</th>
                            <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">有差异</th>
                            <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">无差异</th>
                            <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">未完成</th>
                            <th style="padding: 8px; text-align: center; border: 1px solid #ddd;">失败</th>
                        </tr>
                    </thead>
                    <tbody>
                `;

                    // 添加数据行
                    sortableData.forEach((item, index) => {
                        const rowStyle = index % 2 === 0 ? 'background-color: #f2f2f2;' : 'background-color: white;';
                        const hasDiffStyle = item.hasDiffCnt > 0 ? 'color: red; font-weight: bold;' : '';

                        tableHtml += `
                        <tr style="${rowStyle}">
                            <td style="padding: 8px; text-align: left; border: 1px solid #ddd;">${item.name}</td>
                            <td style="padding: 8px; text-align: center; border: 1px solid #ddd; ${hasDiffStyle}">${item.hasDiffCnt}</td>
                            <td style="padding: 8px; text-align: center; border: 1px solid #ddd;">${item.noDiffCnt}</td>
                            <td style="padding: 8px; text-align: center; border: 1px solid #ddd;">${item.unFinishCnt}</td>
                            <td style="padding: 8px; text-align: center; border: 1px solid #ddd;">${item.failedCnt}</td>
                        </tr>
                    `;
                    });

                    tableHtml += `
                    </tbody>
                </table>
                `;

                    // 将表格显示在页面上
                    dataSpan.innerHTML = tableHtml;
                } else {
                    // 显示原始 JSON 格式（如果格式不符合预期）
                    const formattedJSON = JSON.stringify(data, null, 4);
                    dataSpan.innerHTML = '<pre style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; overflow: auto; font-family: \'Consolas\', \'Monaco\', monospace; font-size: 14px; line-height: 1.4; border: 1px solid #ddd;">' + formattedJSON + '</pre>';
                }

                dataSpan.style.display = 'block';
                dataLoaded = true;
            })
            .catch(error => {
                dataSpan.innerHTML = '<pre style="color: red;">获取数据失败: ' + error.message + '</pre>';
                dataSpan.style.display = 'block';
            });
    }

</script>




<div style="overflow-y: scroll; margin-top: 8px;margin-right: 10px">
    <table class="table table-bordered table-striped table-hover taskList" style="font-size:16px;">
        <tbody>
        <tr style="background-color: #d9d9d9">
            <th style="width:200px">请求参数</th>
            <th style="width:100px">diff数</th>
            <th style="width:200px">before返回值</th>
            <th style="width:200px">new返回值</th>
            <th style="width:120px">diff详情</th>
            <th style="width:120px">接口名</th>
            <th style="width:230px">更新时间</th>
            <th style="width:230px">创建时间</th>
        </tr>
        {{range $key, $diffInfo := .data.DataDiffList}}
        <tr style="height: 46px; font-size: 14px;">
            <td><textarea style="overflow:scroll;height: 250px;width:230px">{{jsonPrettyStr $diffInfo.Params}}</textarea></td>
            <td>{{$diffInfo.DiffNum}}</td>
            <td><textarea style="overflow:scroll;height: 250px;width:230px">{{jsonPrettyStr $diffInfo.OldData}}</textarea></td>
            <td><textarea style="overflow:scroll;height: 250px;width:230px">{{jsonPrettyStr $diffInfo.NewData}}</textarea></td>
            <td><a href="{{$diffInfo.DiffResult}}" style="color: red;" target="_blank">Diff结果</a></td>
            <td>{{$diffInfo.HandlerName}}</td>
            <td>{{showTime $diffInfo.UpdateTime}}</td>
            <td>{{showTime $diffInfo.CreateTime}}</td>
        </tr>
        {{end}}
        </tbody>
    </table>
</div>

{{end}}


